"use client";

import React, { Suspense, lazy } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Loader2 } from "lucide-react";

// Lazy load the RouteMap component for better performance
const RouteMap = lazy(() => 
  import("./route-map").then(module => ({ default: module.RouteMap }))
);

interface LazyRouteMapProps {
  rfqId: string;
  // Cached coordinate data from RFQ
  originLat?: number | null;
  originLng?: number | null;
  destinationLat?: number | null;
  destinationLng?: number | null;
  routeDistanceKm?: number | null;
  // Cache metadata
  coordinatesResolvedAt?: string | null;
  routeDistanceCalculatedAt?: string | null;
  // Address fallback data
  originCity?: string;
  originAddress?: string;
  originPostalCode?: string;
  originCountryName?: string;
  destinationCity?: string;
  destinationAddress?: string;
  destinationPostalCode?: string;
  destinationCountryName?: string;
  className?: string;
}

/**
 * Lazy-loaded Route Map Component Wrapper
 * 
 * This component implements lazy loading for the RouteMap component to improve
 * initial page load performance. The map components are only loaded when needed.
 */
export function LazyRouteMap(props: LazyRouteMapProps) {
  return (
    <Suspense fallback={<RouteMapSkeleton />}>
      <RouteMap {...props} />
    </Suspense>
  );
}

/**
 * Loading skeleton for the route map component
 */
function RouteMapSkeleton() {
  return (
    <div className="space-y-6">
      {/* Route Information Card Skeleton */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MapPin className="h-5 w-5 text-blue-600" />
            Route Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Distance Display Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Distance:</span>
            <Skeleton className="h-6 w-20" />
          </div>

          {/* Estimated Duration Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Estimated Duration:</span>
            <Skeleton className="h-6 w-20" />
          </div>

          {/* Route Status Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Route Status:</span>
            <Skeleton className="h-6 w-16" />
          </div>

          {/* Address Information Skeleton */}
          <div className="space-y-2 pt-2 border-t">
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Origin</p>
                <Skeleton className="h-4 w-full max-w-xs" />
              </div>
            </div>
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Destination</p>
                <Skeleton className="h-4 w-full max-w-xs" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map Visualization Card Skeleton */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MapPin className="h-5 w-5 text-blue-600" />
            Route Map
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Map Container Skeleton */}
          <div className="relative w-full h-96 rounded-lg overflow-hidden border bg-muted">
            <div className="absolute inset-0 flex items-center justify-center bg-muted">
              <div className="text-center space-y-3">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32 mx-auto" />
                  <Skeleton className="h-3 w-24 mx-auto" />
                </div>
                <p className="text-sm text-muted-foreground">Loading map components...</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
